<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Three.js 动画线条示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            background-color: #000;
            color: white;
        }

        canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        #info {
            position: absolute;
            top: 20px;
            left: 0;
            width: 100%;
            text-align: center;
            z-index: 2;
            padding: 10px;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        p {
            font-size: 16px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>

    <!-- 引入Three.js库 -->
    <script src="https://unpkg.com/three@0.152.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.152.0/examples/js/controls/OrbitControls.js"></script>

    <script>
        // 使用全局变量，库已经通过script标签加载

        // 创建场景
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x000000); // 黑色背景

        // 创建相机
        const camera = new THREE.PerspectiveCamera(
          75, // 视野角度
          window.innerWidth / window.innerHeight, // 宽高比
          0.1, // 近平面
          1000 // 远平面
        );
        camera.position.set(0, 0, 50); // 设置相机位置

        // 创建渲染器
        const renderer = new THREE.WebGLRenderer({
          canvas: document.getElementById('canvas'),
          antialias: true // 抗锯齿
        });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 设置像素比

        // 添加轨道控制器，使用户可以旋转和缩放场景
        const controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true; // 启用阻尼效果，使控制器更平滑
        controls.dampingFactor = 0.05;

        // 生成心形曲线的点
        const points = [];
        for (let t = 0; t <= Math.PI * 2; t += 0.01) {
          // 心形曲线的数学公式
          const x = 16 * Math.pow(Math.sin(t), 3);
          const y =
            13 * Math.cos(t) -
            5 * Math.cos(2 * t) -
            2 * Math.cos(3 * t) -
            Math.cos(4 * t);
          points.push(new THREE.Vector3(x, y, 0));
        }

        // 创建心形线条几何体
        const geometry = new THREE.BufferGeometry().setFromPoints(points);

        // 创建线条材质
        const lineMaterial = new THREE.LineBasicMaterial({
          color: 0xff0066, // 粉红色
          linewidth: 2, // 线条宽度
        });

        // 创建线条并添加到场景
        const heartLine = new THREE.Line(geometry, lineMaterial);
        scene.add(mesh);

        // 添加环境光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);

        // 添加方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);

        // 处理窗口大小变化
        window.addEventListener('resize', () => {
          // 更新相机宽高比
          camera.aspect = window.innerWidth / window.innerHeight;
          camera.updateProjectionMatrix();
          
          // 更新渲染器大小
          renderer.setSize(window.innerWidth, window.innerHeight);
          
          // 更新线条材质的分辨率
          lineMaterial.resolution.set(window.innerWidth, window.innerHeight);
        });

        // 动画循环
        function animate() {
          requestAnimationFrame(animate);
          
          // 更新控制器
          controls.update();
          
          // 旋转心形线条
          mesh.rotation.z += 0.005;
          
          // 更新虚线偏移量，创建动画效果
          lineMaterial.dashOffset -= 0.01;
          
          // 渲染场景
          renderer.render(scene, camera);
        }

        // 启动动画循环
        animate();

        // 添加一些额外的视觉效果
        // 创建星星背景
        function addStars() {
          const starsGeometry = new THREE.BufferGeometry();
          const starsMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 0.1,
          });

          const starsVertices = [];
          for (let i = 0; i < 1000; i++) {
            const x = (Math.random() - 0.5) * 2000;
            const y = (Math.random() - 0.5) * 2000;
            const z = (Math.random() - 0.5) * 2000;
            starsVertices.push(x, y, z);
          }

          starsGeometry.setAttribute(
            'position',
            new THREE.Float32BufferAttribute(starsVertices, 3)
          );

          const stars = new THREE.Points(starsGeometry, starsMaterial);
          scene.add(stars);
        }

        // 添加星星
        addStars();
    </script>
</body>
</html>
